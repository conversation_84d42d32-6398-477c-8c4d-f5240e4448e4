
package com.mycompany.inventorymanagement;

import java.awt.BorderLayout;
import java.awt.Font;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.NumberFormat;
import java.util.Locale;

import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;

public class BussinessFrame extends JFrame {
    private JTextArea resultArea;
    private NumberFormat currencyFormat;

    public BussinessFrame() {
        setTitle("Xử lý nghiệp vụ");
        setSize(600, 500);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        // Khởi tạo định dạng tiền tệ Việt Nam
        currencyFormat = NumberFormat.getInstance(new Locale("vi", "VN"));

        JPanel panel = new JPanel(new BorderLayout());
        JButton calcButton = new JButton("Tính tổng giá trị");
        calcButton.addActionListener(e -> calculateTotal());
        JButton showAllButton = new JButton("Hiển thị tất cả sản phẩm");
        showAllButton.addActionListener(e -> showAllProducts());
        JButton backButton = new JButton("Quay lại");
        backButton.addActionListener(e -> {
            new MainFrame().setVisible(true);
            dispose();
        });

        JButton lowStockButton = new JButton("Cảnh báo hết hàng");
        lowStockButton.addActionListener(e -> showLowStockAlert());

        JPanel topPanel = new JPanel();
        topPanel.add(calcButton);
        topPanel.add(showAllButton);
        topPanel.add(lowStockButton);
        topPanel.add(backButton);

        resultArea = new JTextArea(15, 50);
        resultArea.setEditable(false);
        resultArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(resultArea);

        panel.add(topPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);

        add(panel);
    }

    private void calculateTotal() {
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT SUM(quantity * price) AS total FROM products";
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            if (rs.next()) {
                double total = rs.getDouble("total");
                resultArea.setText("Tổng giá trị hàng hóa trong kho: " + currencyFormat.format(total));
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void showAllProducts() {
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT * FROM products ORDER BY id";
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            StringBuilder result = new StringBuilder();
            result.append("DANH SÁCH TẤT CẢ SẢN PHẨM\n");
            result.append("═══════════════════════════════════════════════════════════════\n\n");

            int count = 0;
            double totalValue = 0;

            while (rs.next()) {
                count++;
                double itemValue = rs.getInt("quantity") * rs.getDouble("price");
                totalValue += itemValue;

                result.append("ID: ").append(rs.getInt("id")).append("\n");
                result.append("Tên sản phẩm: ").append(rs.getString("name")).append("\n");
                result.append("Số lượng: ").append(rs.getInt("quantity")).append("\n");
                result.append("Giá đơn vị: ").append(currencyFormat.format(rs.getDouble("price"))).append("\n");
                result.append("Giá trị: ").append(currencyFormat.format(itemValue)).append("\n");
                result.append("Mô tả: ").append(rs.getString("description")).append("\n");
                result.append("─────────────────────────────────────────────────────────────\n\n");
            }

            if (count == 0) {
                result.append("Không có sản phẩm nào trong kho!\n");
            } else {
                result.append("═══════════════════════════════════════════════════════════════\n");
                result.append("TỔNG KẾT:\n");
                result.append("Tổng số sản phẩm: ").append(count).append("\n");
                result.append("Tổng giá trị kho hàng: ").append(currencyFormat.format(totalValue)).append("\n");
            }

            resultArea.setText(result.toString());

        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void showLowStockAlert() {
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = """
                SELECT p.id, p.name, COALESCE(c.name, 'Không có') as category_name,
                       p.quantity, p.min_stock, p.price
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.quantity <= p.min_stock
                ORDER BY (p.quantity - p.min_stock), p.name
                """;
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            StringBuilder result = new StringBuilder();
            result.append("⚠️ CẢNH BÁO SẢN PHẨM SẮP HẾT HÀNG ⚠️\n");
            result.append("═══════════════════════════════════════════════════════════════\n\n");

            int alertCount = 0;
            double totalLossValue = 0;

            while (rs.next()) {
                alertCount++;
                int shortage = rs.getInt("min_stock") - rs.getInt("quantity");
                double lossValue = shortage > 0 ? shortage * rs.getDouble("price") : 0;
                totalLossValue += lossValue;

                result.append("🔴 ID: ").append(rs.getInt("id")).append("\n");
                result.append("📦 Sản phẩm: ").append(rs.getString("name")).append("\n");
                result.append("📂 Danh mục: ").append(rs.getString("category_name")).append("\n");
                result.append("📊 Tồn kho hiện tại: ").append(rs.getInt("quantity")).append("\n");
                result.append("⚠️ Tồn kho tối thiểu: ").append(rs.getInt("min_stock")).append("\n");

                if (shortage > 0) {
                    result.append("❌ Thiếu: ").append(shortage).append(" sản phẩm\n");
                    result.append("💰 Giá trị thiếu hụt: ").append(currencyFormat.format(lossValue)).append("\n");
                } else {
                    result.append("⚠️ Đang ở mức tối thiểu\n");
                }

                result.append("─────────────────────────────────────────────────────────────\n\n");
            }

            if (alertCount == 0) {
                result.append("✅ Tất cả sản phẩm đều đủ hàng!\n");
                result.append("Không có sản phẩm nào cần cảnh báo.\n");
            } else {
                result.append("═══════════════════════════════════════════════════════════════\n");
                result.append("📋 TỔNG KẾT CẢNH BÁO:\n");
                result.append("🔢 Số sản phẩm cần chú ý: ").append(alertCount).append("\n");
                if (totalLossValue > 0) {
                    result.append("💸 Tổng giá trị thiếu hụt: ").append(currencyFormat.format(totalLossValue)).append("\n");
                }
                result.append("\n💡 Khuyến nghị: Cần nhập thêm hàng cho các sản phẩm trên!\n");
            }

            resultArea.setText(result.toString());

        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }
}
