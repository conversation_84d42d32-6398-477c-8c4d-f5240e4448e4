import javax.swing.UIManager;

public class TestLookAndFeel {
    public static void main(String[] args) {
        System.out.println("Available Look and Feel methods:");
        
        try {
            System.out.println("System LAF: " + UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            System.out.println("getSystemLookAndFeel() not available: " + e.getMessage());
        }
        
        try {
            System.out.println("Cross Platform LAF: " + UIManager.getCrossPlatformLookAndFeel());
        } catch (Exception e) {
            System.out.println("getCrossPlatformLookAndFeel() not available: " + e.getMessage());
        }
        
        // List all available look and feels
        UIManager.LookAndFeelInfo[] lafs = UIManager.getInstalledLookAndFeels();
        System.out.println("\nInstalled Look and Feels:");
        for (UIManager.LookAndFeelInfo laf : lafs) {
            System.out.println("- " + laf.getName() + ": " + laf.getClassName());
        }
    }
}
