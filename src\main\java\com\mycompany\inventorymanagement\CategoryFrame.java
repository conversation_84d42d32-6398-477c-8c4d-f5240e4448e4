package com.mycompany.inventorymanagement;

import java.awt.BorderLayout;
import java.awt.GridLayout;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.NumberFormat;
import java.util.Locale;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.table.DefaultTableModel;

public class CategoryFrame extends J<PERSON>rame {
    private JTable table;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JTextField nameField, quantityField, priceField, minStockField, descriptionField;
    private JComboBox<String> categoryComboBox;
    private NumberFormat currencyFormat;

    public CategoryFrame() {
        setTitle("Danh mục hàng hóa");
        setSize(800, 500);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        // Khởi tạo định dạng tiền tệ Việt Nam
        currencyFormat = NumberFormat.getInstance(new Locale("vi", "VN"));

        JPanel mainPanel = new JPanel(new BorderLayout());
        JPanel inputPanel = new JPanel(new GridLayout(6, 2, 5, 5));
        inputPanel.setBorder(BorderFactory.createTitledBorder("Thông tin sản phẩm"));

        inputPanel.add(new JLabel("Tên sản phẩm:"));
        nameField = new JTextField();
        inputPanel.add(nameField);

        inputPanel.add(new JLabel("Danh mục:"));
        categoryComboBox = new JComboBox<>();
        loadCategories();
        inputPanel.add(categoryComboBox);

        inputPanel.add(new JLabel("Số lượng:"));
        quantityField = new JTextField();
        inputPanel.add(quantityField);

        inputPanel.add(new JLabel("Giá bán (VND):"));
        priceField = new JTextField();
        inputPanel.add(priceField);

        inputPanel.add(new JLabel("Tồn kho tối thiểu:"));
        minStockField = new JTextField("10");
        inputPanel.add(minStockField);

        inputPanel.add(new JLabel("Mô tả:"));
        descriptionField = new JTextField();
        inputPanel.add(descriptionField);

        JButton addButton = new JButton("Thêm");
        addButton.addActionListener(e -> addProduct());
        JButton updateButton = new JButton("Sửa");
        updateButton.addActionListener(e -> updateProduct());
        JButton deleteButton = new JButton("Xóa");
        deleteButton.addActionListener(e -> deleteProduct());
        JButton backButton = new JButton("Quay lại");
        backButton.addActionListener(e -> {
            new MainFrame().setVisible(true);
            dispose();
        });

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(addButton);
        buttonPanel.add(updateButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(backButton);

        JPanel searchPanel = new JPanel();
        searchField = new JTextField(20);
        JButton searchButton = new JButton("Tìm kiếm");
        searchButton.addActionListener(e -> searchProduct());
        searchPanel.add(new JLabel("Tìm kiếm: "));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);

        tableModel = new DefaultTableModel(new String[]{"ID", "Tên", "Danh mục", "Số lượng", "Giá", "Tồn kho tối thiểu", "Mô tả"}, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        table = new JTable(tableModel);
        JScrollPane scrollPane = new JScrollPane(table);
        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int row = table.getSelectedRow();
                if (row != -1) {
                    nameField.setText(tableModel.getValueAt(row, 1).toString());
                    // Set category
                    String categoryName = tableModel.getValueAt(row, 2).toString();
                    for (int i = 0; i < categoryComboBox.getItemCount(); i++) {
                        if (categoryComboBox.getItemAt(i).contains(categoryName)) {
                            categoryComboBox.setSelectedIndex(i);
                            break;
                        }
                    }
                    quantityField.setText(tableModel.getValueAt(row, 3).toString());
                    priceField.setText(tableModel.getValueAt(row, 4).toString().replaceAll("[^0-9.]", ""));
                    minStockField.setText(tableModel.getValueAt(row, 5).toString());
                    descriptionField.setText(tableModel.getValueAt(row, 6).toString());
                }
            }
        });

        mainPanel.add(searchPanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(inputPanel, BorderLayout.WEST);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        add(mainPanel);
        loadProducts();
    }

    private void loadCategories() {
        categoryComboBox.removeAllItems();
        categoryComboBox.addItem("0 - Không có danh mục");
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT id, name FROM categories ORDER BY name";
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            while (rs.next()) {
                categoryComboBox.addItem(rs.getInt("id") + " - " + rs.getString("name"));
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void loadProducts() {
        tableModel.setRowCount(0);
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = """
                SELECT p.id, p.name, COALESCE(c.name, 'Không có') as category_name,
                       p.quantity, p.price, p.min_stock, p.description
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                ORDER BY p.name
                """;
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            while (rs.next()) {
                tableModel.addRow(new Object[]{
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("category_name"),
                    rs.getInt("quantity"),
                    currencyFormat.format(rs.getDouble("price")), // Format tiền VND
                    rs.getInt("min_stock"),
                    rs.getString("description")
                });
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void addProduct() {
        String name = nameField.getText().trim();
        String quantityStr = quantityField.getText().trim();
        String priceStr = priceField.getText().trim();
        String minStockStr = minStockField.getText().trim();
        String description = descriptionField.getText().trim();

        if (!validateProduct(name, quantityStr, priceStr, minStockStr)) {
            return;
        }

        int quantity = Integer.parseInt(quantityStr);
        double price = Double.parseDouble(priceStr);
        int minStock = Integer.parseInt(minStockStr);

        // Lấy category ID
        String categoryItem = categoryComboBox.getSelectedItem().toString();
        Integer categoryId = null;
        if (!categoryItem.startsWith("0 -")) {
            categoryId = Integer.parseInt(categoryItem.split(" - ")[0]);
        }

        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "INSERT INTO products (name, category_id, quantity, price, min_stock, description) VALUES (?, ?, ?, ?, ?, ?)";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, name);
            if (categoryId != null) {
                stmt.setInt(2, categoryId);
            } else {
                stmt.setNull(2, Types.INTEGER);
            }
            stmt.setInt(3, quantity);
            stmt.setDouble(4, price);
            stmt.setInt(5, minStock);
            stmt.setString(6, description);
            stmt.executeUpdate();
            JOptionPane.showMessageDialog(this, "Thêm sản phẩm thành công!");
            loadProducts();
            clearFields();
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void updateProduct() {
        int row = table.getSelectedRow();
        if (row != -1) { // Sửa lỗi: nên là row != -1
            String name = nameField.getText().trim();
            String quantityStr = quantityField.getText().trim();
            String priceStr = priceField.getText().trim();
            String minStockStr = minStockField.getText().trim();
            String description = descriptionField.getText().trim();
            if (!validateProduct(name, quantityStr, priceStr, minStockStr)) {
                return;
            }

            int quantity = Integer.parseInt(quantityStr);
            double price = Double.parseDouble(priceStr);
            int minStock = Integer.parseInt(minStockStr);
            int id = (int) tableModel.getValueAt(row, 0);

            // Lấy category ID
            String categoryItem = categoryComboBox.getSelectedItem().toString();
            Integer categoryId = null;
            if (!categoryItem.startsWith("0 -")) {
                categoryId = Integer.parseInt(categoryItem.split(" - ")[0]);
            }

            try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
                String sql = "UPDATE products SET name = ?, category_id = ?, quantity = ?, price = ?, min_stock = ?, description = ? WHERE id = ?";
                PreparedStatement stmt = conn.prepareStatement(sql);
                stmt.setString(1, name);
                if (categoryId != null) {
                    stmt.setInt(2, categoryId);
                } else {
                    stmt.setNull(2, Types.INTEGER);
                }
                stmt.setInt(3, quantity);
                stmt.setDouble(4, price);
                stmt.setInt(5, minStock);
                stmt.setString(6, description);
                stmt.setInt(7, id); // Sửa lỗi: đúng thứ tự parameter
                stmt.executeUpdate();
                JOptionPane.showMessageDialog(this, "Cập nhật sản phẩm thành công!");
                loadProducts();
                clearFields();
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        } else {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn sản phẩm để sửa!");
        }
    }

    private void deleteProduct() {
        int row = table.getSelectedRow();
        if (row != -1) {
            int id = (int) tableModel.getValueAt(row, 0);
            try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
                String sql = "DELETE FROM products WHERE id = ?";
                PreparedStatement stmt = conn.prepareStatement(sql);
                stmt.setInt(1, id);
                stmt.executeUpdate();
                JOptionPane.showMessageDialog(this, "Xóa sản phẩm thành công!");
                loadProducts();
                clearFields();
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        } else {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn sản phẩm để xóa!");
        }
    }

    private void searchProduct() {
        String searchText = searchField.getText().trim();
        tableModel.setRowCount(0);
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT * FROM products WHERE name LIKE ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, "%" + searchText + "%");
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                tableModel.addRow(new Object[]{
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getInt("quantity"),
                    currencyFormat.format(rs.getDouble("price")), // Format tiền VND
                    rs.getString("description")
                });
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private boolean validateProduct(String name, String quantityStr, String priceStr, String minStockStr) {
        if (name.isEmpty() || quantityStr.isEmpty() || priceStr.isEmpty() || minStockStr.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập đầy đủ thông tin!");
            return false;
        }
        // Sửa validation: cho phép tất cả ký tự Unicode (bao gồm tiếng Việt), số, khoảng trắng và dấu câu cơ bản
        if (!name.matches("^[\\p{L}\\p{N}\\p{P}\\p{Z}]+$")) {
            JOptionPane.showMessageDialog(this, "Tên sản phẩm chứa ký tự không hợp lệ!");
            return false;
        }
        try {
            int quantity = Integer.parseInt(quantityStr);
            if (quantity < 0) {
                JOptionPane.showMessageDialog(this, "Số lượng không được âm!");
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Số lượng phải là số nguyên!");
            return false;
        }
        try {
            double price = Double.parseDouble(priceStr);
            if (price < 0) {
                JOptionPane.showMessageDialog(this, "Giá không được âm!");
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Giá phải là số!");
            return false;
        }
        try {
            int minStock = Integer.parseInt(minStockStr);
            if (minStock < 0) {
                JOptionPane.showMessageDialog(this, "Tồn kho tối thiểu không được âm!");
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Tồn kho tối thiểu phải là số nguyên!");
            return false;
        }
        return true;
    }

    private void clearFields() {
        nameField.setText("");
        quantityField.setText("");
        priceField.setText("");
        minStockField.setText("10");
        descriptionField.setText("");
        if (categoryComboBox.getItemCount() > 0) {
            categoryComboBox.setSelectedIndex(0);
        }
        table.clearSelection();
    }
}