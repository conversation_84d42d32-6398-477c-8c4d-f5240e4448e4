package com.mycompany.inventorymanagement; 
 
import java.awt.GridLayout; 
import java.sql.*; 
import javax.swing.*; 
 
public class LoginFrame extends J<PERSON>rame { 
    private JTextField usernameField; 
    private JPasswordField passwordField; 
 
    public LoginFrame() { 
        setTitle("Dang nhap"); 
        setSize(300, 200); 
        setDefaultCloseOperation(EXIT_ON_CLOSE); 
        setLocationRelativeTo(null); 
 
        JPanel panel = new JPanel(new GridLayout(3, 2, 5, 5)); 
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10)); 
 
        panel.add(new JLabel("Ten dang nhap:")); 
        usernameField = new JTextField(); 
        panel.add(usernameField); 
 
        panel.add(new JLabel("Mat khau:")); 
        passwordField = new JPasswordField(); 
        panel.add(passwordField); 
 
        JButton loginButton = new JButton("Dang nhap"); 
        loginButton.addActionListener(e -> login()); 
        panel.add(loginButton); 
        panel.add(new JLabel("")); 
 
        add(panel); 
    } 
 
    private void login() { 
        String username = usernameField.getText().trim(); 
        String password = new String(passwordField.getPassword()); 
        if (username.equals("admin") && password.equals("admin123")) { 
            JOptionPane.showMessageDialog(this, "Dang nhap thanh cong!"); 
            new MainFrame().setVisible(true); 
            dispose(); 
        } else { 
            JOptionPane.showMessageDialog(this, "Sai thong tin!"); 
        } 
    } 
 
    public static void main(String[] args) { 
        SwingUtilities.invokeLater(() -> new LoginFrame().setVisible(true)); 
    } 
} 
