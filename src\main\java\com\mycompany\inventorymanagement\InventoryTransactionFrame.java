package com.mycompany.inventorymanagement;

import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.awt.event.*;
import java.sql.*;
import java.text.NumberFormat;
import java.util.Locale;

public class InventoryTransactionFrame extends JFrame {
    private JTable table;
    private DefaultTableModel tableModel;
    private JComboBox<String> productComboBox;
    private JComboBox<String> transactionTypeComboBox;
    private JTextField quantityField, priceField, reasonField, referenceField;
    private NumberFormat currencyFormat;

    public InventoryTransactionFrame() {
        setTitle("Quản lý nhập/xuất kho");
        setSize(900, 600);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        
        currencyFormat = NumberFormat.getInstance(new Locale("vi", "VN"));

        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // Panel nhập liệu
        JPanel inputPanel = new JPanel(new GridLayout(6, 2, 5, 5));
        inputPanel.setBorder(BorderFactory.createTitledBorder("Thông tin giao dịch"));
        
        inputPanel.add(new JLabel("Sản phẩm:"));
        productComboBox = new JComboBox<>();
        loadProducts();
        inputPanel.add(productComboBox);
        
        inputPanel.add(new JLabel("Loại giao dịch:"));
        transactionTypeComboBox = new JComboBox<>(new String[]{"IN", "OUT"});
        inputPanel.add(transactionTypeComboBox);
        
        inputPanel.add(new JLabel("Số lượng:"));
        quantityField = new JTextField();
        inputPanel.add(quantityField);
        
        inputPanel.add(new JLabel("Giá (VND):"));
        priceField = new JTextField();
        inputPanel.add(priceField);
        
        inputPanel.add(new JLabel("Lý do:"));
        reasonField = new JTextField();
        inputPanel.add(reasonField);
        
        inputPanel.add(new JLabel("Số chứng từ:"));
        referenceField = new JTextField();
        inputPanel.add(referenceField);

        // Panel nút bấm
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton addButton = new JButton("Thực hiện giao dịch");
        addButton.addActionListener(e -> addTransaction());
        JButton refreshButton = new JButton("Làm mới");
        refreshButton.addActionListener(e -> {
            loadTransactions();
            loadProducts();
        });
        JButton backButton = new JButton("Quay lại");
        backButton.addActionListener(e -> {
            new MainFrame().setVisible(true);
            dispose();
        });

        buttonPanel.add(addButton);
        buttonPanel.add(refreshButton);
        buttonPanel.add(backButton);

        // Bảng hiển thị lịch sử giao dịch
        tableModel = new DefaultTableModel(new String[]{
            "ID", "Sản phẩm", "Loại", "Số lượng", "Giá", "Lý do", "Số chứng từ", "Ngày"
        }, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        table = new JTable(tableModel);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Lịch sử giao dịch"));

        // Layout chính
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.add(inputPanel, BorderLayout.NORTH);
        topPanel.add(buttonPanel, BorderLayout.SOUTH);

        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        add(mainPanel);
        loadTransactions();
    }

    private void loadProducts() {
        productComboBox.removeAllItems();
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT id, name, quantity FROM products ORDER BY name";
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String item = rs.getInt("id") + " - " + rs.getString("name") + 
                             " (Tồn: " + rs.getInt("quantity") + ")";
                productComboBox.addItem(item);
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void loadTransactions() {
        tableModel.setRowCount(0);
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = """
                SELECT it.id, p.name as product_name, it.transaction_type, 
                       it.quantity, it.price, it.reason, it.reference_number,
                       DATE_FORMAT(it.created_at, '%d/%m/%Y %H:%i') as created_date
                FROM inventory_transactions it
                JOIN products p ON it.product_id = p.id
                ORDER BY it.created_at DESC
                LIMIT 100
                """;
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String type = rs.getString("transaction_type").equals("IN") ? "Nhập kho" : "Xuất kho";
                String price = rs.getDouble("price") > 0 ? currencyFormat.format(rs.getDouble("price")) : "";
                
                tableModel.addRow(new Object[]{
                    rs.getInt("id"),
                    rs.getString("product_name"),
                    type,
                    rs.getInt("quantity"),
                    price,
                    rs.getString("reason"),
                    rs.getString("reference_number"),
                    rs.getString("created_date")
                });
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void addTransaction() {
        if (productComboBox.getSelectedItem() == null) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn sản phẩm!");
            return;
        }

        String productItem = productComboBox.getSelectedItem().toString();
        int productId = Integer.parseInt(productItem.split(" - ")[0]);
        String transactionType = transactionTypeComboBox.getSelectedItem().toString();
        String quantityStr = quantityField.getText().trim();
        String priceStr = priceField.getText().trim();
        String reason = reasonField.getText().trim();
        String reference = referenceField.getText().trim();

        if (quantityStr.isEmpty() || reason.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập đầy đủ thông tin!");
            return;
        }

        try {
            int quantity = Integer.parseInt(quantityStr);
            double price = priceStr.isEmpty() ? 0 : Double.parseDouble(priceStr);

            if (quantity <= 0) {
                JOptionPane.showMessageDialog(this, "Số lượng phải lớn hơn 0!");
                return;
            }

            // Kiểm tra tồn kho nếu là xuất kho
            if (transactionType.equals("OUT")) {
                if (!checkStock(productId, quantity)) {
                    return;
                }
            }

            // Thực hiện giao dịch
            try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
                conn.setAutoCommit(false);

                // Thêm giao dịch
                String insertSql = """
                    INSERT INTO inventory_transactions 
                    (product_id, transaction_type, quantity, price, reason, reference_number, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, 'admin')
                    """;
                PreparedStatement insertStmt = conn.prepareStatement(insertSql);
                insertStmt.setInt(1, productId);
                insertStmt.setString(2, transactionType);
                insertStmt.setInt(3, quantity);
                insertStmt.setDouble(4, price);
                insertStmt.setString(5, reason);
                insertStmt.setString(6, reference);
                insertStmt.executeUpdate();

                // Cập nhật tồn kho
                String updateSql = "UPDATE products SET quantity = quantity " + 
                                  (transactionType.equals("IN") ? "+" : "-") + " ? WHERE id = ?";
                PreparedStatement updateStmt = conn.prepareStatement(updateSql);
                updateStmt.setInt(1, quantity);
                updateStmt.setInt(2, productId);
                updateStmt.executeUpdate();

                conn.commit();
                JOptionPane.showMessageDialog(this, "Giao dịch thành công!");
                loadTransactions();
                loadProducts();
                clearFields();

            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }

        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(this, "Số lượng và giá phải là số hợp lệ!");
        }
    }

    private boolean checkStock(int productId, int quantity) {
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT name, quantity FROM products WHERE id = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setInt(1, productId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                int currentStock = rs.getInt("quantity");
                if (currentStock < quantity) {
                    JOptionPane.showMessageDialog(this, 
                        "Không đủ hàng trong kho!\n" +
                        "Tồn kho hiện tại: " + currentStock + "\n" +
                        "Số lượng xuất: " + quantity);
                    return false;
                }
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kiểm tra tồn kho: " + ex.getMessage());
            return false;
        }
        return true;
    }

    private void clearFields() {
        quantityField.setText("");
        priceField.setText("");
        reasonField.setText("");
        referenceField.setText("");
        if (productComboBox.getItemCount() > 0) {
            productComboBox.setSelectedIndex(0);
        }
        transactionTypeComboBox.setSelectedIndex(0);
    }
}
