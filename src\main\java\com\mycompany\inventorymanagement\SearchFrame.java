package com.mycompany.inventorymanagement;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.sql.*;
import java.text.NumberFormat;
import java.util.Locale;

public class SearchFrame extends JFrame {
    private JTextField searchField;
    private JTextArea resultArea;
    private NumberFormat currencyFormat;

    public SearchFrame() {
        initializeUI();
    }

    private void initializeUI() {
        setTitle("Tra cứu hàng hóa");
        setSize(700, 600);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setResizable(true);

        // Khởi tạo định dạng tiền tệ Việt Nam
        currencyFormat = NumberFormat.getCurrencyInstance(new Locale("vi", "VN"));

        // Main layout
        setLayout(new BorderLayout());

        // Header
        add(createHeaderPanel(), BorderLayout.NORTH);

        // Content
        add(createContentPanel(), BorderLayout.CENTER);
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel();
        headerPanel.setBackground(new Color(52, 152, 219));
        headerPanel.setLayout(new BorderLayout());
        headerPanel.setPreferredSize(new Dimension(0, 80));
        headerPanel.setBorder(new EmptyBorder(15, 30, 15, 30));

        // Title
        JLabel titleLabel = new JLabel("🔍 TRA CỨU HÀNG HÓA");
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 20));
        titleLabel.setForeground(Color.WHITE);

        // Back button
        JButton backButton = createStyledButton("← Quay lại");
        backButton.addActionListener(e -> {
            new MainFrame().setVisible(true);
            dispose();
        });

        headerPanel.add(titleLabel, BorderLayout.WEST);
        headerPanel.add(backButton, BorderLayout.EAST);

        return headerPanel;
    }

    private JPanel createContentPanel() {
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(new Color(236, 240, 241));
        contentPanel.setBorder(new EmptyBorder(20, 20, 20, 20));

        // Search panel
        JPanel searchPanel = createSearchPanel();

        // Results panel
        JPanel resultsPanel = createResultsPanel();

        contentPanel.add(searchPanel, BorderLayout.NORTH);
        contentPanel.add(resultsPanel, BorderLayout.CENTER);

        return contentPanel;
    }

    private JPanel createSearchPanel() {
        JPanel searchPanel = new JPanel();
        searchPanel.setBackground(Color.WHITE);
        searchPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(189, 195, 199), 1),
            new EmptyBorder(20, 20, 20, 20)
        ));
        searchPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 10, 10));

        JLabel searchLabel = new JLabel("Tên hàng hóa:");
        searchLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
        searchLabel.setForeground(new Color(44, 62, 80));

        searchField = new JTextField(25);
        searchField.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        searchField.setPreferredSize(new Dimension(300, 35));
        searchField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(189, 195, 199), 1),
            new EmptyBorder(5, 10, 5, 10)
        ));

        JButton searchButton = createStyledButton("🔍 Tìm kiếm");
        searchButton.addActionListener(e -> search());

        // Add Enter key support
        searchField.addActionListener(e -> search());

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        searchPanel.add(searchButton);

        return searchPanel;
    }

    private JPanel createResultsPanel() {
        JPanel resultsPanel = new JPanel(new BorderLayout());
        resultsPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createLineBorder(new Color(189, 195, 199), 1),
            "Kết quả tìm kiếm",
            0, 0,
            new Font("Segoe UI", Font.BOLD, 14),
            new Color(44, 62, 80)
        ));
        resultsPanel.setBackground(Color.WHITE);

        resultArea = new JTextArea(15, 50);
        resultArea.setEditable(false);
        resultArea.setFont(new Font("Segoe UI", Font.PLAIN, 13));
        resultArea.setBackground(new Color(248, 249, 250));
        resultArea.setBorder(new EmptyBorder(10, 10, 10, 10));
        resultArea.setText("Nhập tên hàng hóa và nhấn 'Tìm kiếm' để bắt đầu...");

        JScrollPane scrollPane = new JScrollPane(resultArea);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());

        resultsPanel.add(scrollPane, BorderLayout.CENTER);

        return resultsPanel;
    }

    private JButton createStyledButton(String text) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 14));
        button.setPreferredSize(new Dimension(140, 35));
        button.setBackground(new Color(46, 204, 113));
        button.setForeground(Color.WHITE);
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // Hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(new Color(39, 174, 96));
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(new Color(46, 204, 113));
            }
        });

        return button;
    }

    private void search() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            showStyledMessage("Vui lòng nhập tên hàng hóa!", "Cảnh báo", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Show loading message
        resultArea.setText("Đang tìm kiếm...");
        resultArea.setForeground(new Color(52, 73, 94));

        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT p.*, c.name as category_name FROM products p " +
                        "LEFT JOIN categories c ON p.category_id = c.id " +
                        "WHERE p.name LIKE ? ORDER BY p.name";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, "%" + searchText + "%");
            ResultSet rs = stmt.executeQuery();

            StringBuilder result = new StringBuilder();
            int count = 0;

            result.append("KET QUA TIM KIEM CHO: \"").append(searchText).append("\"\n");
            result.append("================================================\n\n");

            while (rs.next()) {
                count++;
                result.append("SAN PHAM #").append(count).append("\n");
                result.append("----------------------------------------\n");
                result.append("ID: ").append(rs.getInt("id")).append("\n");
                result.append("Ten: ").append(rs.getString("name")).append("\n");

                String categoryName = rs.getString("category_name");
                if (categoryName != null) {
                    result.append("Danh muc: ").append(categoryName).append("\n");
                }

                result.append("So luong: ").append(rs.getInt("quantity")).append(" san pham\n");
                result.append("Gia: ").append(currencyFormat.format(rs.getDouble("price"))).append("\n");
                result.append("Ton kho toi thieu: ").append(rs.getInt("min_stock")).append(" san pham\n");

                String description = rs.getString("description");
                if (description != null && !description.trim().isEmpty()) {
                    result.append("Mo ta: ").append(description).append("\n");
                }

                result.append("----------------------------------------\n\n");
            }

            if (count > 0) {
                result.append("Tim thay ").append(count).append(" san pham phu hop.\n");
                resultArea.setForeground(new Color(39, 174, 96));
            } else {
                result.setLength(0);
                result.append("KHONG TIM THAY KET QUA\n");
                result.append("================================================\n\n");
                result.append("Khong tim thay san pham nao voi tu khoa: \"").append(searchText).append("\"\n\n");
                result.append("Goi y:\n");
                result.append("- Kiem tra lai chinh ta\n");
                result.append("- Thu su dung tu khoa ngan hon\n");
                result.append("- Tim kiem theo ten danh muc\n");
                resultArea.setForeground(new Color(231, 76, 60));
            }

            resultArea.setText(result.toString());

        } catch (SQLException ex) {
            resultArea.setText("LOI KET NOI DATABASE\n" +
                             "================================================\n\n" +
                             "Chi tiet loi: " + ex.getMessage() + "\n\n" +
                             "Hay kiem tra:\n" +
                             "- Database da duoc khoi dong chua?\n" +
                             "- Thong tin ket noi co dung khong?\n" +
                             "- Quyen truy cap database");
            resultArea.setForeground(new Color(231, 76, 60));
        }
    }

    private void showStyledMessage(String message, String title, int messageType) {
        JOptionPane.showMessageDialog(this, message, title, messageType);
    }
}