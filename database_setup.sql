-- Tạo database cho hệ thống quản lý hàng hóa
CREATE DATABASE IF NOT EXISTS inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE inventory_db;

-- Tạo bảng users để lưu thông tin người dùng (chỉ cho admin)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL, -- Mật khẩu đã được hash bằng BCrypt
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tạo bảng categories để lưu danh mục sản phẩm
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tạo bảng products để lưu thông tin sản phẩm
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category_id INT,
    quantity INT NOT NULL DEFAULT 0,
    price DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- Sử dụng DECIMAL cho tiền tệ
    min_stock INT DEFAULT 10, -- Mức tồn kho tối thiểu
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_price (price),
    INDEX idx_category (category_id)
);

-- Thêm dữ liệu mẫu cho bảng categories
INSERT INTO categories (name, description) VALUES
('Nội thất', 'Đồ nội thất văn phòng và gia đình'),
('Điện tử', 'Thiết bị điện tử và công nghệ'),
('Phụ kiện máy tính', 'Phụ kiện và linh kiện máy tính'),
('Âm thanh', 'Thiết bị âm thanh và giải trí'),
('Văn phòng phẩm', 'Dụng cụ và thiết bị văn phòng'),
('Thời trang', 'Quần áo và phụ kiện thời trang'),
('Gia dụng', 'Đồ dùng gia đình và nhà bếp'),
('Sách và tạp chí', 'Sách báo và tài liệu học tập'),
('Thể thao', 'Dụng cụ và thiết bị thể thao'),
('Làm đẹp', 'Mỹ phẩm và sản phẩm chăm sóc sắc đẹp');

-- Thêm dữ liệu mẫu cho bảng products
INSERT INTO products (name, category_id, quantity, price, min_stock, description) VALUES
-- Nội thất (category_id = 1)
('Bàn gỗ sồi', 1, 10, 1500000.00, 5, 'Bàn làm việc bằng gỗ sồi tự nhiên'),
('Ghế xoay văn phòng', 1, 25, 850000.00, 10, 'Ghế xoay có tựa lưng cao'),
('Tủ sách gỗ', 1, 8, 2200000.00, 3, 'Tủ sách 5 tầng bằng gỗ công nghiệp'),
('Sofa da 3 chỗ', 1, 5, 4500000.00, 2, 'Sofa da thật màu nâu sang trọng'),
('Bàn trà kính', 1, 12, 980000.00, 5, 'Bàn trà mặt kính cường lực'),
('Giường ngủ 1m8', 1, 6, 3200000.00, 2, 'Giường ngủ gỗ MDF có hộc tủ'),
('Tủ quần áo 3 cánh', 1, 4, 2800000.00, 2, 'Tủ quần áo gỗ công nghiệp'),

-- Điện tử (category_id = 2)
('Máy tính để bàn', 2, 8, 12000000.00, 3, 'Máy tính cấu hình cao cho văn phòng'),
('Điện thoại thông minh', 2, 30, 8500000.00, 15, 'Smartphone Android mới nhất'),
('Màn hình LCD 24 inch', 2, 15, 3200000.00, 8, 'Màn hình Full HD IPS'),
('Laptop Dell Inspiron', 2, 12, 15000000.00, 5, 'Laptop văn phòng Core i5'),
('Tablet Samsung', 2, 20, 6500000.00, 8, 'Tablet Android 10 inch'),
('Máy ảnh Canon', 2, 7, 18000000.00, 3, 'Máy ảnh DSLR chuyên nghiệp'),
('Tivi Smart 43 inch', 2, 9, 8900000.00, 4, 'Smart TV 4K Android'),

-- Phụ kiện máy tính (category_id = 3)
('Bàn phím cơ', 3, 40, 1200000.00, 15, 'Bàn phím cơ gaming RGB'),
('Chuột không dây', 3, 60, 350000.00, 20, 'Chuột quang không dây 2.4GHz'),
('Webcam HD', 3, 25, 850000.00, 10, 'Webcam 1080p cho họp online'),
('USB 3.0 32GB', 3, 100, 180000.00, 30, 'USB tốc độ cao 32GB'),
('Ổ cứng di động 1TB', 3, 18, 1400000.00, 8, 'Ổ cứng ngoài USB 3.0'),
('Hub USB 4 cổng', 3, 35, 250000.00, 15, 'Hub chia USB 4 cổng'),

-- Âm thanh (category_id = 4)
('Tai nghe Bluetooth', 4, 100, 450000.00, 30, 'Tai nghe không dây chất lượng cao'),
('Loa Bluetooth', 4, 35, 750000.00, 15, 'Loa di động chống nước'),
('Micro thu âm', 4, 15, 1200000.00, 5, 'Micro condenser chuyên nghiệp'),
('Loa vi tính 2.1', 4, 22, 680000.00, 10, 'Loa vi tính có subwoofer'),
('Headphone gaming', 4, 28, 950000.00, 12, 'Headphone gaming có mic'),

-- Văn phòng phẩm (category_id = 5)
('Bút bi Thiên Long', 5, 500, 5000.00, 100, 'Bút bi xanh đỏ đen'),
('Giấy A4 Double A', 5, 200, 85000.00, 50, 'Giấy photocopy A4 70gsm'),
('Máy tính Casio', 5, 45, 320000.00, 15, 'Máy tính để bàn 12 số'),
('Kẹp tài liệu', 5, 150, 15000.00, 50, 'Kẹp tài liệu kim loại'),
('Hộp bút nhựa', 5, 80, 25000.00, 30, 'Hộp đựng bút nhiều ngăn'),
('Bảng từ trắng', 5, 12, 450000.00, 5, 'Bảng từ trắng 60x90cm'),

-- Thời trang (category_id = 6)
('Áo sơ mi nam', 6, 50, 280000.00, 20, 'Áo sơ mi cotton trắng'),
('Quần jean nữ', 6, 35, 450000.00, 15, 'Quần jean skinny xanh đậm'),
('Giày thể thao', 6, 40, 850000.00, 15, 'Giày thể thao nam nữ'),
('Túi xách nữ', 6, 25, 650000.00, 10, 'Túi xách da PU cao cấp'),
('Đồng hồ nam', 6, 18, 1200000.00, 8, 'Đồng hồ thép không gỉ'),

-- Gia dụng (category_id = 7)
('Nồi cơm điện', 7, 30, 680000.00, 12, 'Nồi cơm điện 1.8L'),
('Máy xay sinh tố', 7, 22, 450000.00, 10, 'Máy xay đa năng 2L'),
('Bình đun siêu tốc', 7, 45, 280000.00, 18, 'Bình đun nước inox 1.7L'),
('Chảo chống dính', 7, 60, 180000.00, 25, 'Chảo chống dính 26cm'),
('Bộ dao nhà bếp', 7, 35, 320000.00, 15, 'Bộ dao inox 6 món'),

-- Sách và tạp chí (category_id = 8)
('Sách lập trình Java', 8, 25, 180000.00, 10, 'Giáo trình lập trình Java cơ bản'),
('Tạp chí Công nghệ', 8, 100, 25000.00, 30, 'Tạp chí công nghệ hàng tháng'),
('Sách tiếng Anh', 8, 40, 120000.00, 15, 'Sách học tiếng Anh giao tiếp'),
('Truyện tranh', 8, 80, 35000.00, 30, 'Truyện tranh Nhật Bản'),

-- Thể thao (category_id = 9)
('Bóng đá FIFA', 9, 20, 350000.00, 8, 'Bóng đá da PU size 5'),
('Vợt cầu lông', 9, 15, 480000.00, 6, 'Vợt cầu lông carbon'),
('Giày chạy bộ', 9, 25, 1200000.00, 10, 'Giày chạy bộ chuyên dụng'),
('Thảm yoga', 9, 30, 180000.00, 12, 'Thảm yoga chống trượt'),

-- Làm đẹp (category_id = 10)
('Kem dưỡng da', 10, 50, 280000.00, 20, 'Kem dưỡng ẩm ban đêm'),
('Sữa rửa mặt', 10, 60, 150000.00, 25, 'Sữa rửa mặt cho da nhạy cảm'),
('Son môi', 10, 80, 120000.00, 30, 'Son môi lâu trôi nhiều màu'),
('Nước hoa', 10, 25, 850000.00, 10, 'Nước hoa nam nữ 50ml');

-- Tạo bảng inventory_transactions để lưu lịch sử nhập/xuất kho
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    transaction_type ENUM('IN', 'OUT') NOT NULL, -- IN: Nhập kho, OUT: Xuất kho
    quantity INT NOT NULL,
    price DECIMAL(15,2), -- Giá nhập/xuất
    reason VARCHAR(255), -- Lý do nhập/xuất
    reference_number VARCHAR(100), -- Số chứng từ
    created_by VARCHAR(50), -- Người thực hiện
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product (product_id),
    INDEX idx_type (transaction_type),
    INDEX idx_date (created_at)
);

-- Thêm user admin mặc định (password: admin123)
-- Tạm thời dùng plain text cho demo, production nên dùng BCrypt
INSERT INTO users (username, password, role) VALUES
('admin', 'admin123', 'admin'),
('demo', 'demo123', 'user');

-- Thêm dữ liệu mẫu cho bảng inventory_transactions
INSERT INTO inventory_transactions (product_id, transaction_type, quantity, price, reason, reference_number, created_by) VALUES
-- Nhập kho
(1, 'IN', 20, 1500000.00, 'Nhập hàng đầu tháng', 'NK001', 'admin'),
(2, 'IN', 30, 850000.00, 'Nhập hàng từ nhà cung cấp', 'NK002', 'admin'),
(3, 'IN', 15, 2200000.00, 'Nhập hàng mới', 'NK003', 'admin'),
(10, 'IN', 50, 8500000.00, 'Nhập điện thoại mới', 'NK004', 'admin'),
(15, 'IN', 100, 1200000.00, 'Nhập bàn phím gaming', 'NK005', 'admin'),

-- Xuất kho
(1, 'OUT', 5, 1500000.00, 'Bán cho khách hàng', 'XK001', 'admin'),
(2, 'OUT', 8, 850000.00, 'Bán lẻ', 'XK002', 'admin'),
(10, 'OUT', 12, 8500000.00, 'Bán sỉ cho đại lý', 'XK003', 'admin'),
(15, 'OUT', 25, 1200000.00, 'Bán online', 'XK004', 'admin'),
(20, 'OUT', 3, 450000.00, 'Khuyến mãi', 'XK005', 'admin');

-- Tạo view để xem thống kê tổng quan
CREATE VIEW product_summary AS
SELECT 
    COUNT(*) as total_products,
    SUM(quantity) as total_quantity,
    SUM(quantity * price) as total_value,
    AVG(price) as average_price,
    MIN(price) as min_price,
    MAX(price) as max_price
FROM products;

-- Tạo stored procedure để tìm kiếm sản phẩm
DELIMITER //
CREATE PROCEDURE SearchProducts(IN search_term VARCHAR(255))
BEGIN
    SELECT * FROM products 
    WHERE name LIKE CONCAT('%', search_term, '%')
    ORDER BY name;
END //
DELIMITER ;

-- Tạo stored procedure để cập nhật số lượng sản phẩm
DELIMITER //
CREATE PROCEDURE UpdateProductQuantity(
    IN product_id INT, 
    IN new_quantity INT
)
BEGIN
    UPDATE products 
    SET quantity = new_quantity, 
        updated_at = CURRENT_TIMESTAMP 
    WHERE id = product_id;
END //
DELIMITER ;

-- Tạo trigger để log khi có thay đổi sản phẩm
CREATE TABLE IF NOT EXISTS product_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT,
    action_type ENUM('INSERT', 'UPDATE', 'DELETE'),
    old_data JSON,
    new_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

DELIMITER //
CREATE TRIGGER product_update_log 
AFTER UPDATE ON products
FOR EACH ROW
BEGIN
    INSERT INTO product_logs (product_id, action_type, old_data, new_data)
    VALUES (
        NEW.id, 
        'UPDATE',
        JSON_OBJECT('name', OLD.name, 'quantity', OLD.quantity, 'price', OLD.price),
        JSON_OBJECT('name', NEW.name, 'quantity', NEW.quantity, 'price', NEW.price)
    );
END //
DELIMITER ;

-- Hiển thị thông tin database đã tạo
SELECT 'Database setup completed successfully!' as status;
SELECT * FROM product_summary;
