package com.mycompany.inventorymanagement;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.*;
import java.awt.*;
import java.awt.event.*;
import java.sql.*;

public class CategoryManagementFrame extends J<PERSON>rame {
    private JTable table;
    private DefaultTableModel tableModel;
    private JTextField nameField, descriptionField;

    public CategoryManagementFrame() {
        initializeUI();
        loadCategories();
    }

    private void initializeUI() {
        setTitle("Quản lý danh mục sản phẩm");
        setSize(900, 600);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setResizable(false);

        // Main panel with gradient background
        JPanel mainPanel = createGradientPanel();
        mainPanel.setLayout(new BorderLayout());
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));

        // Header panel
        JPanel headerPanel = createHeaderPanel();

        // Content panel
        JPanel contentPanel = createContentPanel();

        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(contentPanel, BorderLayout.CENTER);

        add(mainPanel);
    }

    private JPanel createGradientPanel() {
        return new JPanel() {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                int w = getWidth(), h = getHeight();
                Color color1 = new Color(52, 152, 219);
                Color color2 = new Color(41, 128, 185);
                GradientPaint gp = new GradientPaint(0, 0, color1, 0, h, color2);
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
            }
        };
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setOpaque(false);
        headerPanel.setBorder(new EmptyBorder(0, 0, 20, 0));

        // Title
        JLabel titleLabel = new JLabel("QUẢN LÝ DANH MỤC", SwingConstants.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);

        // Subtitle
        JLabel subtitleLabel = new JLabel("Quản lý danh mục sản phẩm", SwingConstants.CENTER);
        subtitleLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        subtitleLabel.setForeground(new Color(255, 255, 255, 180));

        JPanel titlePanel = new JPanel(new GridLayout(2, 1, 0, 5));
        titlePanel.setOpaque(false);
        titlePanel.add(titleLabel);
        titlePanel.add(subtitleLabel);

        headerPanel.add(titlePanel, BorderLayout.CENTER);

        return headerPanel;
    }

    private JPanel createContentPanel() {
        JPanel contentPanel = new JPanel(new BorderLayout(0, 15));
        contentPanel.setOpaque(false);

        // Input panel
        JPanel inputPanel = createInputPanel();

        // Table panel
        JPanel tablePanel = createTablePanel();

        contentPanel.add(inputPanel, BorderLayout.NORTH);
        contentPanel.add(tablePanel, BorderLayout.CENTER);

        return contentPanel;
    }

    private JPanel createInputPanel() {
        JPanel inputPanel = new JPanel();
        inputPanel.setBackground(Color.WHITE);
        inputPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(189, 195, 199), 1),
            new EmptyBorder(20, 20, 20, 20)
        ));
        inputPanel.setLayout(new GridBagLayout());

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // Title
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 4;
        JLabel formTitle = new JLabel("Thông tin danh mục", SwingConstants.CENTER);
        formTitle.setFont(new Font("Segoe UI", Font.BOLD, 16));
        formTitle.setForeground(new Color(44, 62, 80));
        inputPanel.add(formTitle, gbc);

        // Name field
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1;
        gbc.anchor = GridBagConstraints.WEST;
        JLabel nameLabel = createStyledLabel("Tên danh mục:");
        inputPanel.add(nameLabel, gbc);

        gbc.gridx = 1; gbc.gridy = 1; gbc.weightx = 1.0;
        nameField = createStyledTextField();
        nameField.setPreferredSize(new Dimension(200, 35));
        inputPanel.add(nameField, gbc);

        // Description field
        gbc.gridx = 2; gbc.gridy = 1; gbc.weightx = 0;
        JLabel descLabel = createStyledLabel("Mô tả:");
        inputPanel.add(descLabel, gbc);

        gbc.gridx = 3; gbc.gridy = 1; gbc.weightx = 1.0;
        descriptionField = createStyledTextField();
        descriptionField.setPreferredSize(new Dimension(200, 35));
        inputPanel.add(descriptionField, gbc);

        // Buttons
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 4;
        gbc.insets = new Insets(15, 8, 8, 8);
        JPanel buttonPanel = createButtonPanel();
        inputPanel.add(buttonPanel, gbc);

        return inputPanel;
    }

    private JPanel createButtonPanel() {
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
        buttonPanel.setOpaque(false);

        JButton addButton = createStyledButton("Them", new Color(39, 174, 96));
        addButton.addActionListener(e -> addCategory());

        JButton updateButton = createStyledButton("Sua", new Color(52, 152, 219));
        updateButton.addActionListener(e -> updateCategory());

        JButton deleteButton = createStyledButton("Xoa", new Color(231, 76, 60));
        deleteButton.addActionListener(e -> deleteCategory());

        JButton clearButton = createStyledButton("Lam moi", new Color(149, 165, 166));
        clearButton.addActionListener(e -> clearFields());

        JButton backButton = createStyledButton("Quay lai", new Color(95, 106, 106));
        backButton.addActionListener(e -> {
            new MainFrame().setVisible(true);
            dispose();
        });

        buttonPanel.add(addButton);
        buttonPanel.add(updateButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(clearButton);
        buttonPanel.add(backButton);

        return buttonPanel;
    }

    private JPanel createTablePanel() {
        JPanel tablePanel = new JPanel(new BorderLayout());
        tablePanel.setBackground(Color.WHITE);
        tablePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(189, 195, 199), 1),
            new EmptyBorder(15, 15, 15, 15)
        ));

        // Table title
        JLabel tableTitle = new JLabel("Danh sách danh mục", SwingConstants.CENTER);
        tableTitle.setFont(new Font("Segoe UI", Font.BOLD, 16));
        tableTitle.setForeground(new Color(44, 62, 80));
        tableTitle.setBorder(new EmptyBorder(0, 0, 15, 0));

        // Create table
        tableModel = new DefaultTableModel(new String[]{"ID", "Tên danh mục", "Mô tả", "Số sản phẩm", "Ngày tạo"}, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        table = new JTable(tableModel);
        styleTable();

        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int row = table.getSelectedRow();
                if (row != -1) {
                    nameField.setText(tableModel.getValueAt(row, 1).toString());
                    Object desc = tableModel.getValueAt(row, 2);
                    descriptionField.setText(desc != null ? desc.toString() : "");
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setBorder(BorderFactory.createLineBorder(new Color(189, 195, 199), 1));
        scrollPane.getViewport().setBackground(Color.WHITE);

        tablePanel.add(tableTitle, BorderLayout.NORTH);
        tablePanel.add(scrollPane, BorderLayout.CENTER);

        return tablePanel;
    }

    private void styleTable() {
        table.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        table.setRowHeight(35);
        table.setGridColor(new Color(236, 240, 241));
        table.setSelectionBackground(new Color(52, 152, 219));
        table.setSelectionForeground(Color.WHITE);
        table.setShowGrid(true);
        table.setIntercellSpacing(new Dimension(1, 1));

        // Header styling
        table.getTableHeader().setFont(new Font("Segoe UI", Font.BOLD, 14));
        table.getTableHeader().setBackground(new Color(44, 62, 80));
        table.getTableHeader().setForeground(Color.WHITE);
        table.getTableHeader().setPreferredSize(new Dimension(0, 40));
        table.getTableHeader().setBorder(BorderFactory.createEmptyBorder());

        // Column widths
        table.getColumnModel().getColumn(0).setPreferredWidth(50);  // ID
        table.getColumnModel().getColumn(1).setPreferredWidth(150); // Tên
        table.getColumnModel().getColumn(2).setPreferredWidth(200); // Mô tả
        table.getColumnModel().getColumn(3).setPreferredWidth(100); // Số SP
        table.getColumnModel().getColumn(4).setPreferredWidth(120); // Ngày tạo
    }

    // Helper methods for styling
    private JLabel createStyledLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        label.setForeground(new Color(44, 62, 80));
        return label;
    }

    private JTextField createStyledTextField() {
        JTextField textField = new JTextField();
        textField.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        textField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(189, 195, 199), 1),
            new EmptyBorder(8, 12, 8, 12)
        ));
        return textField;
    }

    private JButton createStyledButton(String text, Color bgColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 14));
        button.setPreferredSize(new Dimension(100, 35));
        button.setBackground(bgColor);
        button.setForeground(Color.WHITE);
        button.setBorder(BorderFactory.createEmptyBorder(8, 15, 8, 15));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // Hover effect
        Color hoverColor = bgColor.darker();
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(hoverColor);
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }



    private void loadCategories() {
        tableModel.setRowCount(0);
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = """
                SELECT c.id, c.name, c.description, 
                       COUNT(p.id) as product_count, 
                       DATE_FORMAT(c.created_at, '%d/%m/%Y %H:%i') as created_date
                FROM categories c 
                LEFT JOIN products p ON c.id = p.category_id 
                GROUP BY c.id, c.name, c.description, c.created_at
                ORDER BY c.name
                """;
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                tableModel.addRow(new Object[]{
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("description"),
                    rs.getInt("product_count"),
                    rs.getString("created_date")
                });
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void addCategory() {
        String name = nameField.getText().trim();
        String description = descriptionField.getText().trim();

        if (name.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập tên danh mục!");
            return;
        }

        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "INSERT INTO categories (name, description) VALUES (?, ?)";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, name);
            stmt.setString(2, description);
            stmt.executeUpdate();
            
            JOptionPane.showMessageDialog(this, "Thêm danh mục thành công!");
            loadCategories();
            clearFields();
        } catch (SQLException ex) {
            if (ex.getErrorCode() == 1062) { // Duplicate entry
                JOptionPane.showMessageDialog(this, "Tên danh mục đã tồn tại!");
            } else {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        }
    }

    private void updateCategory() {
        int row = table.getSelectedRow();
        if (row == -1) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn danh mục để sửa!");
            return;
        }

        String name = nameField.getText().trim();
        String description = descriptionField.getText().trim();

        if (name.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập tên danh mục!");
            return;
        }

        int id = (int) tableModel.getValueAt(row, 0);

        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "UPDATE categories SET name = ?, description = ? WHERE id = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, name);
            stmt.setString(2, description);
            stmt.setInt(3, id);
            stmt.executeUpdate();
            
            JOptionPane.showMessageDialog(this, "Cập nhật danh mục thành công!");
            loadCategories();
            clearFields();
        } catch (SQLException ex) {
            if (ex.getErrorCode() == 1062) { // Duplicate entry
                JOptionPane.showMessageDialog(this, "Tên danh mục đã tồn tại!");
            } else {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        }
    }

    private void deleteCategory() {
        int row = table.getSelectedRow();
        if (row == -1) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn danh mục để xóa!");
            return;
        }

        int productCount = (int) tableModel.getValueAt(row, 3);
        if (productCount > 0) {
            JOptionPane.showMessageDialog(this, 
                "Không thể xóa danh mục này vì còn " + productCount + " sản phẩm!");
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(this, 
            "Bạn có chắc chắn muốn xóa danh mục này?", 
            "Xác nhận xóa", 
            JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            int id = (int) tableModel.getValueAt(row, 0);

            try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
                String sql = "DELETE FROM categories WHERE id = ?";
                PreparedStatement stmt = conn.prepareStatement(sql);
                stmt.setInt(1, id);
                stmt.executeUpdate();
                
                JOptionPane.showMessageDialog(this, "Xóa danh mục thành công!");
                loadCategories();
                clearFields();
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        }
    }

    private void clearFields() {
        nameField.setText("");
        descriptionField.setText("");
        table.clearSelection();
    }
}
